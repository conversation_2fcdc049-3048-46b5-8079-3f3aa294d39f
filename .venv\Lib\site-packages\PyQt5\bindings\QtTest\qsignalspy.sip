// qsignalspy.sip generated by MetaSIP
//
// This file is part of the QtTest Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QSignalSpy : QObject
{
%TypeHeaderCode
#include <qsignalspy.h>
%End

%ConvertToSubClassCode
    sipType = (sipCpp->inherits("QSignalSpy") ? sipType_QSignalSpy : 0);
    
    #if QT_VERSION >= 0x050b00
    if (!sipType && sipCpp->inherits("QAbstractItemModelTester"))
        sipType = sipType_QAbstractItemModelTester;
    #endif
%End

public:
    QSignalSpy(SIP_PYOBJECT signal /TypeHint="pyqtBoundSignal"/) [(const QObject *obj, const char *aSignal)];
%MethodCode
        QObject *sender;
        QByteArray signal_signature;
        
        if ((sipError = pyqt5_qttest_get_pyqtsignal_parts(a0, &sender, signal_signature)) == sipErrorNone)
            sipCpp = new sipQSignalSpy(sender, signal_signature.constData());
        else if (sipError == sipErrorContinue)
            sipError = sipBadCallableArg(0, a0);
%End

%If (Qt_5_14_0 -)
    QSignalSpy(const QObject *obj, const QMetaMethod &signal);
%End
    bool isValid() const;
    QByteArray signal() const;
    bool wait(int timeout = 5000) /ReleaseGIL/;
    int __len__() const;
%MethodCode
        sipRes = sipCpp->count();
%End

    QList<QVariant> __getitem__(int i) const;
%MethodCode
        Py_ssize_t idx = sipConvertFromSequenceIndex(a0, sipCpp->count());
        
        if (idx < 0)
            sipIsErr = 1;
        else
            sipRes = new QList<QVariant>(sipCpp->at((int)idx));
%End

    void __setitem__(int i, const QList<QVariant> &value);
%MethodCode
        int len = sipCpp->count();
        
        if ((a0 = (int)sipConvertFromSequenceIndex(a0, len)) < 0)
            sipIsErr = 1;
        else
            (*sipCpp)[a0] = *a1;
%End

    void __delitem__(int i);
%MethodCode
        if ((a0 = (int)sipConvertFromSequenceIndex(a0, sipCpp->count())) < 0)
            sipIsErr = 1;
        else
            sipCpp->removeAt(a0);
%End
};

%ModuleHeaderCode
// Imports from QtCore.
typedef sipErrorState (*pyqt5_qttest_get_pyqtsignal_parts_t)(PyObject *, QObject **, QByteArray &);
extern pyqt5_qttest_get_pyqtsignal_parts_t pyqt5_qttest_get_pyqtsignal_parts;
%End

%ModuleCode
// Imports from QtCore.
pyqt5_qttest_get_pyqtsignal_parts_t pyqt5_qttest_get_pyqtsignal_parts;
%End

%PostInitialisationCode
// Imports from QtCore.
pyqt5_qttest_get_pyqtsignal_parts = (pyqt5_qttest_get_pyqtsignal_parts_t)sipImportSymbol("pyqt5_get_pyqtsignal_parts");
Q_ASSERT(pyqt5_qttest_get_pyqtsignal_parts);
%End
