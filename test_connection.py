#!/usr/bin/env python3
"""
Test connection to PocketOption
اختبار الاتصال مع PocketOption
"""

import json
import logging
from pocketoptionapi.stable_api import PocketOption

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_connection():
    """Test connection with your session"""
    
    # Load config
    try:
        with open('config.json', 'r') as f:
            config = json.load(f)
    except FileNotFoundError:
        print("❌ config.json not found. Please create it first.")
        return False
    
    session = config.get('session')
    if not session or session == "ضع_session_id_هنا":
        print("❌ Please update session in config.json")
        return False
    
    print("🔄 Testing connection...")
    
    try:
        # Create API instance
        api = PocketOption(session)
        
        # Test connection
        connected, message = api.connect()
        
        if connected:
            print("✅ Connection successful!")
            
            # Switch to practice account for safety
            api.change_balance("PRACTICE")
            print("🎯 Switched to PRACTICE account")
            
            # Get balance
            balance = api.get_balance()
            print(f"💰 Current balance: ${balance}")
            
            # Get available assets
            assets = api.get_payment()
            open_assets = [asset for asset, info in assets.items() if info["open"]]
            print(f"📊 Available assets: {len(open_assets)}")
            
            # Close connection
            api.close()
            print("✅ Test completed successfully!")
            return True
            
        else:
            print(f"❌ Connection failed: {message}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    test_connection()
