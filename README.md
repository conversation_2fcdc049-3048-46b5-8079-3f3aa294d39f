# PocketOption Trading Bot

بوت تداول متقدم لمنصة PocketOption مع ذكاء اصطناعي وتحليل فني شامل.

## الميزات الجديدة المضافة

### 1. تحليل أنماط الثلاث شموع
- Morning Star Pattern (نمط نجمة الصباح)
- Evening Star Pattern (نمط نجمة المساء)
- Three White Soldiers (الجنود البيض الثلاثة)
- Three Black Crows (الغربان السوداء الثلاثة)
- Bullish/Bearish Harami
- Three Inside Up/Down

### 2. نظام الإشعارات المتقدم
- إشعارات البريد الإلكتروني
- إشعارات Telegram
- تنبيهات للصفقات الرابحة/الخاسرة
- تحذيرات المخاطر العالية
- تنبيهات الوصول لحدود الربح/الخسارة

### 3. نظام النسخ الاحتياطي التلقائي
- نسخ احتياطية دورية للبيانات
- حفظ تاريخ الصفقات والإعدادات
- تنظيف النسخ القديمة تلقائياً

### 4. معالجة الأخطاء المحسنة
- إعادة المحاولة التلقائية للعمليات الفاشلة
- تسجيل مفصل للأخطاء
- استقرار أفضل للاتصال

### 5. مراقبة الأخبار الاقتصادية
- تجنب التداول أثناء الأخبار عالية التأثير
- حماية من التقلبات المفاجئة

### 6. مقاييس الأداء المتقدمة
- معدل الربح/الخسارة
- أطول سلسلة ربح/خسارة
- عائد الاستثمار (ROI)
- إحصائيات مفصلة

## التثبيت والإعداد

### 1. تثبيت المتطلبات
```bash
pip install numpy pandas scikit-learn scipy pocketoptionapi requests
```

### 2. إعداد ملف التكوين
1. انسخ `config_template.json` إلى `config.json`
2. قم بتعديل الإعدادات حسب احتياجاتك:
   - `session`: سلسلة الجلسة من PocketOption
   - `amount`: مبلغ التداول
   - `notifications`: إعدادات الإشعارات

### 3. إعداد إشعارات Telegram (اختياري)
1. أنشئ بوت جديد عبر @BotFather
2. احصل على Bot Token
3. احصل على Chat ID الخاص بك
4. فعّل الإشعارات في ملف التكوين

### 4. إعداد إشعارات البريد الإلكتروني (اختياري)
1. استخدم Gmail مع App Password
2. أدخل بيانات SMTP في ملف التكوين
3. فعّل الإشعارات

## الاستخدام

### تشغيل البوت
```bash
python po.py
```

### إيقاف البوت
اضغط `Ctrl+C` لإيقاف البوت بأمان

## الملفات المهمة

- `po.py`: الملف الرئيسي للبوت
- `config.json`: ملف الإعدادات
- `trading_logs/`: مجلد سجلات التداول
- `backups/`: مجلد النسخ الاحتياطية
- `trading_history.json`: تاريخ الصفقات

## إعدادات التكوين المهمة

### إدارة المخاطر
- `risk_per_trade`: نسبة المخاطرة لكل صفقة (افتراضي: 2%)
- `max_consecutive_losses`: أقصى خسائر متتالية (افتراضي: 3)
- `profit_target_percent`: هدف الربح اليومي (افتراضي: 5%)
- `stop_loss_percent`: حد الخسارة اليومي (افتراضي: 5%)

### إعدادات التداول
- `min_signal_score`: أقل نقاط الإشارة للتداول (افتراضي: 60)
- `trading_hours`: ساعات التداول
- `trading_days`: أيام التداول (0=الاثنين، 6=الأحد)

### النسخ الاحتياطي
- `backup.enabled`: تفعيل النسخ الاحتياطي
- `backup.interval_minutes`: فترة النسخ الاحتياطي بالدقائق
- `backup.max_backups`: أقصى عدد نسخ احتياطية

## التحذيرات المهمة

⚠️ **تحذير**: هذا البوت للأغراض التعليمية. التداول ينطوي على مخاطر مالية.

⚠️ **تأكد من**:
- اختبار البوت على الحساب التجريبي أولاً
- مراجعة جميع الإعدادات قبل التشغيل
- مراقبة الأداء باستمرار
- عدم استثمار أموال لا يمكنك تحمل خسارتها

## الدعم والمساعدة

- تحقق من ملفات السجل في مجلد `trading_logs/`
- راجع النسخ الاحتياطية في مجلد `backups/`
- تأكد من صحة إعدادات الاتصال

## التحديثات المستقبلية

- تحليل المشاعر للأخبار
- واجهة مستخدم ويب
- تحليل أعمق للأنماط
- دعم منصات تداول إضافية
