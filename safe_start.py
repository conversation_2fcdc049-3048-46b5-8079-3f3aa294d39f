#!/usr/bin/env python3
"""
Safe start for trading bot
بداية آمنة لبوت التداول
"""

import json
import logging
from po import TradingBot

def safe_start():
    """Start bot with safe settings"""
    
    # Load configuration
    try:
        with open('config.json', 'r') as f:
            config = json.load(f)
    except FileNotFoundError:
        print("❌ config.json not found")
        return
    
    # Override with safe settings for first run
    safe_config = config.copy()
    safe_config.update({
        "amount": 1,                    # مبلغ صغير
        "max_trades_per_day": 3,        # عدد قليل من الصفقات
        "risk_per_trade": 0.005,        # مخاطرة 0.5% فقط
        "profit_target_percent": 2,     # هدف ربح منخفض
        "stop_loss_percent": 2,         # حد خسارة منخفض
        "min_signal_score": 70,         # إشارات قوية فقط
        "backtest_mode": False,
        "notifications": {
            "telegram": {"enabled": False},
            "email": {"enabled": False}
        }
    })
    
    print("🤖 Starting bot with SAFE settings:")
    print(f"   💰 Amount per trade: ${safe_config['amount']}")
    print(f"   📊 Max trades per day: {safe_config['max_trades_per_day']}")
    print(f"   ⚠️  Risk per trade: {safe_config['risk_per_trade']*100}%")
    print(f"   🎯 Profit target: {safe_config['profit_target_percent']}%")
    print(f"   🛑 Stop loss: {safe_config['stop_loss_percent']}%")
    print()
    
    # Ask for confirmation
    confirm = input("Continue with these safe settings? (y/N): ").strip().lower()
    if confirm != 'y':
        print("❌ Cancelled by user")
        return
    
    try:
        # Create and run bot
        bot = TradingBot(safe_config)
        
        # Ask for duration
        duration = input("Enter session duration in hours (default: 0.5): ").strip()
        duration = float(duration) if duration else 0.5
        
        print(f"⏰ Running for {duration} hours with PRACTICE account")
        print("📊 Monitor the logs carefully!")
        print("⏹️  Press Ctrl+C to stop anytime")
        print("-" * 50)
        
        # Run bot
        bot.run_trading_session(duration_hours=duration)
        bot.save_trading_history()
        
        print("\n✅ Session completed successfully!")
        
    except KeyboardInterrupt:
        print("\n⏹️  Bot stopped by user")
    except Exception as e:
        print(f"\n❌ Error: {e}")
        logging.error(f"Bot error: {e}")
    finally:
        if 'bot' in locals():
            bot.close()

if __name__ == "__main__":
    safe_start()
