// qsqlrelationaltablemodel.sip generated by MetaSIP
//
// This file is part of the QtSql Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QSqlRelation
{
%TypeHeaderCode
#include <qsqlrelationaltablemodel.h>
%End

public:
    QSqlRelation();
    QSqlRelation(const QString &aTableName, const QString &indexCol, const QString &displayCol);
    QString tableName() const;
    QString indexColumn() const;
    QString displayColumn() const;
    bool isValid() const;
%If (Qt_5_8_0 -)
    void swap(QSqlRelation &other /Constrained/);
%End
};

class QSqlRelationalTableModel : public QSqlTableModel
{
%TypeHeaderCode
#include <qsqlrelationaltablemodel.h>
%End

public:
    QSqlRelationalTableModel(QObject *parent /TransferThis/ = 0, QSqlDatabase db = QSqlDatabase());
    virtual ~QSqlRelationalTableModel();
    virtual QVariant data(const QModelIndex &item, int role = Qt::ItemDataRole::DisplayRole) const;
    virtual bool setData(const QModelIndex &item, const QVariant &value, int role = Qt::ItemDataRole::EditRole);
    virtual void clear();
    virtual bool select();
    virtual void setTable(const QString &tableName);
    virtual void setRelation(int column, const QSqlRelation &relation);
    QSqlRelation relation(int column) const;
    virtual QSqlTableModel *relationModel(int column) const;
    virtual void revertRow(int row);
    virtual bool removeColumns(int column, int count, const QModelIndex &parent = QModelIndex());

protected:
    virtual QString selectStatement() const;
    virtual bool updateRowInTable(int row, const QSqlRecord &values);
    virtual QString orderByClause() const;
    virtual bool insertRowIntoTable(const QSqlRecord &values);

public:
    enum JoinMode
    {
        InnerJoin,
        LeftJoin,
    };

    void setJoinMode(QSqlRelationalTableModel::JoinMode joinMode);
};
