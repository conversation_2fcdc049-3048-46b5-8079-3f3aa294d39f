import logging
import time
import numpy as np
import pandas as pd
from scipy import stats
from sklearn.ensemble import RandomForestClassifier
from sklearn.preprocessing import StandardScaler
from pocketoptionapi.stable_api import PocketOption
import os
import json
from datetime import datetime

# Configure advanced logging
log_dir = "trading_logs"
os.makedirs(log_dir, exist_ok=True)
log_file = os.path.join(log_dir, f"trading_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler()
    ]
)

# Configuration
CONFIG = {
    "session": r"""42["auth",{"session":"YOUR_SESSION_STRING_HERE"}]""",
    "amount": 1,
    "duration": 60,
    "candle_period": 60,
    "max_trades_per_day": 10,
    "max_consecutive_losses": 3,
    "profit_target_percent": 5,
    "stop_loss_percent": 5,
    "risk_per_trade": 0.02,  # 2% of balance
    "min_signal_score": 60,
    "min_win_rate": 0.55,
    "backtest_mode": False,
    "trading_hours": {
        "start": 9,  # 9 AM
        "end": 17    # 5 PM
    },
    "trading_days": [0, 1, 2, 3, 4]  # Monday to Friday (0 = Monday, 6 = Sunday)
}

class TradingBot:
    def __init__(self, config):
        self.config = config
        self.account = PocketOption(config["session"])
        self.connected = False
        self.trade_history = []
        self.daily_trades = 0
        self.consecutive_losses = 0
        self.starting_balance = 0
        self.current_balance = 0
        self.ml_models = {}
        
    def connect(self):
        """Connect to the trading platform"""
        connected, message = self.account.connect()
        if not connected:
            logging.error(f"Connection failed: {message}")
            return False
            
        logging.info("✅ Connected to PocketOption")
        self.account.change_balance("PRACTICE")
        self.connected = True
        self.starting_balance = self.account.get_balance()
        self.current_balance = self.starting_balance
        logging.info(f"Starting balance: ${self.starting_balance}")
        return True
        
    def fetch_candles(self, asset, count=100):
        """Fetch historical candles for analysis"""
        now = int(time.time())
        offset = count * self.config["candle_period"]
        candles = self.account.get_candle(asset, now, offset, self.config["candle_period"])
        return candles["data"][-count:]
        
    def prepare_dataframe(self, candles):
        """Convert candles to pandas DataFrame with all necessary indicators"""
        df = pd.DataFrame(candles)
        df['open'] = pd.to_numeric(df['open'])
        df['close'] = pd.to_numeric(df['close'])
        df['high'] = pd.to_numeric(df['high'])
        df['low'] = pd.to_numeric(df['low'])
        df['volume'] = pd.to_numeric(df['volume'])
        
        # Calculate indicators
        self._calculate_indicators(df)
        
        return df
        
    def _calculate_indicators(self, df):
        """Calculate all technical indicators"""
        # Basic price data
        df['returns'] = df['close'].pct_change()
        df['range'] = df['high'] - df['low']
        
        # Moving Averages
        for period in [5, 8, 13, 21, 34, 55]:
            df[f'sma_{period}'] = df['close'].rolling(window=period).mean()
            df[f'ema_{period}'] = df['close'].ewm(span=period).mean()
        
        # MACD
        df['ema12'] = df['close'].ewm(span=12).mean()
        df['ema26'] = df['close'].ewm(span=26).mean()
        df['macd'] = df['ema12'] - df['ema26']
        df['macd_signal'] = df['macd'].ewm(span=9).mean()
        df['macd_hist'] = df['macd'] - df['macd_signal']
        
        # RSI
        delta = df['close'].diff()
        gain = delta.where(delta > 0, 0).rolling(window=14).mean()
        loss = -delta.where(delta < 0, 0).rolling(window=14).mean()
        rs = gain / loss
        df['rsi'] = 100 - (100 / (1 + rs))
        
        # Bollinger Bands
        df['bb_middle'] = df['close'].rolling(window=20).mean()
        df['bb_std'] = df['close'].rolling(window=20).std()
        df['bb_upper'] = df['bb_middle'] + 2 * df['bb_std']
        df['bb_lower'] = df['bb_middle'] - 2 * df['bb_std']
        df['bb_width'] = (df['bb_upper'] - df['bb_lower']) / df['bb_middle']
        
        # Stochastic Oscillator
        df['lowest_14'] = df['low'].rolling(window=14).min()
        df['highest_14'] = df['high'].rolling(window=14).max()
        df['stoch_k'] = 100 * ((df['close'] - df['lowest_14']) / (df['highest_14'] - df['lowest_14']))
        df['stoch_d'] = df['stoch_k'].rolling(window=3).mean()
        
        # ADX (Average Directional Index)
        df['tr1'] = abs(df['high'] - df['low'])
        df['tr2'] = abs(df['high'] - df['close'].shift())
        df['tr3'] = abs(df['low'] - df['close'].shift())
        df['tr'] = df[['tr1', 'tr2', 'tr3']].max(axis=1)
        df['atr'] = df['tr'].rolling(window=14).mean()
        
        # Directional Movement Index (DMI)
        df['plus_dm'] = df['high'].diff()
        df['minus_dm'] = df['low'].diff()
        df['plus_dm'] = df['plus_dm'].where((df['plus_dm'] > 0) & (df['plus_dm'] > df['minus_dm'].abs()), 0)
        df['minus_dm'] = df['minus_dm'].abs().where((df['minus_dm'] > 0) & (df['minus_dm'] > df['plus_dm']), 0)
        df['plus_di'] = 100 * (df['plus_dm'].rolling(window=14).mean() / df['atr'])
        df['minus_di'] = 100 * (df['minus_dm'].rolling(window=14).mean() / df['atr'])
        df['dx'] = 100 * (abs(df['plus_di'] - df['minus_di']) / (df['plus_di'] + df['minus_di']))
        df['adx'] = df['dx'].rolling(window=14).mean()
        
        # Ichimoku Cloud
        df['tenkan_sen'] = (df['high'].rolling(window=9).max() + df['low'].rolling(window=9).min()) / 2
        df['kijun_sen'] = (df['high'].rolling(window=26).max() + df['low'].rolling(window=26).min()) / 2
        df['senkou_span_a'] = ((df['tenkan_sen'] + df['kijun_sen']) / 2).shift(26)
        df['senkou_span_b'] = ((df['high'].rolling(window=52).max() + df['low'].rolling(window=52).min()) / 2).shift(26)
        df['chikou_span'] = df['close'].shift(-26)
        
        # Momentum
        df['momentum'] = df['close'] - df['close'].shift(10)
        
        # Rate of Change
        df['roc'] = df['close'].pct_change(10) * 100
        
        # Williams %R
        df['williams_r'] = -100 * (df['highest_14'] - df['close']) / (df['highest_14'] - df['lowest_14'])
        
        # On-Balance Volume (OBV)
        df['obv'] = (np.sign(df['close'].diff()) * df['volume']).fillna(0).cumsum()
        
        # Fibonacci Levels (based on recent swing high/low)
        window = 20
        df['swing_high'] = df['high'].rolling(window=window, center=True).max()
        df['swing_low'] = df['low'].rolling(window=window, center=True).min()
        df['fib_range'] = df['swing_high'] - df['swing_low']
        df['fib_38.2'] = df['swing_high'] - 0.382 * df['fib_range']
        df['fib_50.0'] = df['swing_high'] - 0.5 * df['fib_range']
        df['fib_61.8'] = df['swing_high'] - 0.618 * df['fib_range']
        
        return df
    
    def detect_patterns(self, df):
        """Detect candlestick and chart patterns"""
        patterns = {}
        
        # Doji pattern
        doji_threshold = 0.1
        df['doji'] = abs(df['close'] - df['open']) <= doji_threshold * (df['high'] - df['low'])
        patterns['doji'] = df['doji'].iloc[-1]
        
        # Hammer pattern
        body_size = abs(df['close'] - df['open'])
        lower_wick = df.apply(lambda x: min(x['open'], x['close']) - x['low'], axis=1)
        upper_wick = df.apply(lambda x: x['high'] - max(x['open'], x['close']), axis=1)
        
        hammer_condition = (
            (body_size.iloc[-1] <= 0.3 * (df['high'].iloc[-1] - df['low'].iloc[-1])) &
            (lower_wick.iloc[-1] >= 2 * body_size.iloc[-1]) &
            (upper_wick.iloc[-1] <= 0.1 * (df['high'].iloc[-1] - df['low'].iloc[-1]))
        )
        patterns['hammer'] = hammer_condition
        
        # Engulfing patterns
        bullish_engulfing = (
            (df['close'].iloc[-2] < df['open'].iloc[-2]) &  # Previous candle is bearish
            (df['close'].iloc[-1] > df['open'].iloc[-1]) &  # Current candle is bullish
            (df['close'].iloc[-1] > df['open'].iloc[-2]) &  # Current close > previous open
            (df['open'].iloc[-1] < df['close'].iloc[-2])    # Current open < previous close
        )
        patterns['bullish_engulfing'] = bullish_engulfing
        
        bearish_engulfing = (
            (df['close'].iloc[-2] > df['open'].iloc[-2]) &  # Previous candle is bullish
            (df['close'].iloc[-1] < df['open'].iloc[-1]) &  # Current candle is bearish
            (df['close'].iloc[-1] < df['open'].iloc[-2]) &  # Current close < previous open
            (df['open'].iloc[-1] > df['close'].iloc[-2])    # Current open > previous close
        )
        patterns['bearish_engulfing'] = bearish_engulfing
        
        # Double top/bottom patterns
        lookback = 20
        if len(df) >= lookback:
            recent_highs = df['high'][-lookback:].values
            recent_lows = df['low'][-lookback:].values
            
            # Find peaks and troughs
            peak_indices = []
            trough_indices = []
            
            for i in range(1, lookback-1):
                if recent_highs[i] > recent_highs[i-1] and recent_highs[i] > recent_highs[i+1]:
                    peak_indices.append(i)
                if recent_lows[i] < recent_lows[i-1] and recent_lows[i] < recent_lows[i+1]:
                    trough_indices.append(i)
            
            # Check for double top
            if len(peak_indices) >= 2:
                peak1, peak2 = peak_indices[-2], peak_indices[-1]
                if abs(recent_highs[peak1] - recent_highs[peak2]) / recent_highs[peak1] < 0.02:  # Within 2%
                    patterns['double_top'] = True
            
            # Check for double bottom
            if len(trough_indices) >= 2:
                trough1, trough2 = trough_indices[-2], trough_indices[-1]
                if abs(recent_lows[trough1] - recent_lows[trough2]) / recent_lows[trough1] < 0.02:  # Within 2%
                    patterns['double_bottom'] = True
        
        return patterns
    
    def train_ml_model(self, asset):
        """Train a machine learning model for the asset"""
        try:
            # Get historical data
            candles = self.fetch_candles(asset, 500)
            if len(candles) < 200:  # Need sufficient data
                return None
            
            df = self.prepare_dataframe(candles)
            df = df.dropna()  # Remove rows with NaN values
        
            # Prepare features and target
            features = [
                'rsi', 'macd', 'macd_hist', 'stoch_k', 'stoch_d', 
                'bb_width', 'atr', 'momentum', 'roc', 'williams_r'
            ]
        
            # Add moving average features
            for period in [5, 8, 13, 21, 34]:
                features.append(f'sma_{period}')
                features.append(f'ema_{period}')
        
            # Create target: 1 if price goes up in next candle, 0 otherwise
            df['target'] = (df['close'].shift(-1) > df['close']).astype(int)
        
            # Split data
            X = df[features].iloc[:-1]  # All except last row
            y = df['target'].iloc[:-1]   # All except last row
        
            # Standardize features
            scaler = StandardScaler()
            X_scaled = scaler.fit_transform(X)
        
            # Train model
            model = RandomForestClassifier(n_estimators=100, random_state=42)
            model.fit(X_scaled, y)
        
            # Save model and scaler
            self.ml_models[asset] = {
                'model': model,
                'scaler': scaler,
                'features': features
            }
        
            logging.info(f"Trained ML model for {asset}")
            return model
        
        except Exception as e:
            logging.warning(f"Error training ML model for {asset}: {e}")
            return None
    
    def predict_with_ml(self, asset, df):
        """Make prediction using the trained ML model"""
        if asset not in self.ml_models:
            self.train_ml_model(asset)
            
        if asset not in self.ml_models:
            return None
            
        try:
            model_data = self.ml_models[asset]
            model = model_data['model']
            scaler = model_data['scaler']
            features = model_data['features']
            
            # Prepare features
            X = df[features].iloc[-1:].values
            X_scaled = scaler.transform(X)
            
            # Predict
            prediction = model.predict_proba(X_scaled)[0]
            
            # Return probability of price going up
            return prediction[1]
            
        except Exception as e:
            logging.warning(f"Error predicting with ML for {asset}: {e}")
            return None
    
    def analyze_market(self, asset):
        """Comprehensive market analysis using multiple algorithms"""
        try:
            # Get candles
            candles = self.fetch_candles(asset, 100)
            if len(candles) < 50:
                return None, 0
            
            # Prepare data
            df = self.prepare_dataframe(candles)
            
            # Detect patterns
            patterns = self.detect_patterns(df)
            
            # Analyze three candles patterns
            three_candle_signal, three_candle_score = self.analyze_three_candles(df)
            
            # Machine learning prediction
            ml_prediction = self.predict_with_ml(asset, df)
            
            # Get latest values
            latest = df.iloc[-1]
            
            # Scoring system (0-100)
            bull_score = 0
            bear_score = 0
            
            # RSI signals
            if latest['rsi'] < 30:
                bull_score += 15  # Oversold
            elif latest['rsi'] > 70:
                bear_score += 15  # Overbought
                
            # Moving average signals
            if latest['close'] > latest['sma_5'] > latest['sma_21']:
                bull_score += 10  # Bullish trend
            elif latest['close'] < latest['sma_5'] < latest['sma_21']:
                bear_score += 10  # Bearish trend
                
            # MACD signals
            if latest['macd'] > latest['macd_signal'] and latest['macd_hist'] > 0:
                bull_score += 10
            elif latest['macd'] < latest['macd_signal'] and latest['macd_hist'] < 0:
                bear_score += 10
                
            # Bollinger Bands signals
            if latest['close'] < latest['bb_lower']:
                bull_score += 10  # Potential bounce
            elif latest['close'] > latest['bb_upper']:
                bear_score += 10  # Potential reversal
                
            # Stochastic signals
            if latest['stoch_k'] < 20 and latest['stoch_k'] > latest['stoch_d']:
                bull_score += 10
            elif latest['stoch_k'] > 80 and latest['stoch_k'] < latest['stoch_d']:
                bear_score += 10
                
            # ADX signals
            if 'adx' in latest and latest['adx'] > 25:
                if latest['plus_di'] > latest['minus_di']:
                    bull_score += 10  # Strong uptrend
                elif latest['minus_di'] > latest['plus_di']:
                    bear_score += 10  # Strong downtrend
            
            # Pattern recognition
            if patterns.get('hammer', False):
                bull_score += 10
            if patterns.get('bullish_engulfing', False):
                bull_score += 10
            if patterns.get('bearish_engulfing', False):
                bear_score += 10
            if patterns.get('double_top', False):
                bear_score += 15
            if patterns.get('double_bottom', False):
                bull_score += 15
                
            # Ichimoku Cloud signals
            if latest['close'] > latest['senkou_span_a'] and latest['close'] > latest['senkou_span_b']:
                bull_score += 10  # Price above the cloud
            elif latest['close'] < latest['senkou_span_a'] and latest['close'] < latest['senkou_span_b']:
                bear_score += 10  # Price below the cloud
                
            # Machine learning contribution
            if ml_prediction is not None:
                if ml_prediction > 0.65:
                    bull_score += 15
                elif ml_prediction < 0.35:
                    bear_score += 15
            
            # Add three candle pattern score if available
            if three_candle_signal == "call":
                bull_score += three_candle_score
            elif three_candle_signal == "put":
                bear_score += three_candle_score
            
            # Determine signal
            if bull_score >= self.config["min_signal_score"] and bull_score > bear_score + 20:
                return "call", bull_score
            elif bear_score >= self.config["min_signal_score"] and bear_score > bull_score + 20:
                return "put", bear_score
            
            return None, 0
        
        except Exception as e:
            logging.warning(f"Analysis error for {asset}: {e}")
            return None, 0
    
    def find_best_asset(self):
        """Find the best asset to trade based on signals and profit potential"""
        all_assets = self.account.get_payment()
        best_assets = []
        
        for asset in all_assets:
            if not all_assets[asset]["open"]:
                continue  # Skip closed assets
                
            signal, score = self.analyze_market(asset)
            if signal:
                profit = all_assets[asset]["profit"] / 100  # Convert to decimal
                # Calculate a combined score that considers both signal strength and profit
                combined_score = score * profit
                best_assets.append((asset, signal, profit, score, combined_score))
        
        # Sort by combined score
        best_assets.sort(key=lambda x: x[4], reverse=True)
        
        if best_assets:
            asset, signal, profit, score, _ = best_assets[0]
            logging.info(f"✅ Selected asset: {asset} with {signal.upper()} signal (score: {score}, profit: {profit*100}%)")
            return asset, signal
        
        return None, None
    
    def calculate_position_size(self):
        """Calculate position size based on risk management rules"""
        # Get current balance
        balance = self.account.get_balance()
        
        # Calculate position size based on risk per trade
        position_size = balance * self.config["risk_per_trade"]
        
        # Round to appropriate size
        position_size = max(1, round(position_size))
        
        return position_size
    
    def should_trade(self):
        """Determine if we should trade based on various conditions"""
        # Check if we've reached the maximum trades for the day
        if self.daily_trades >= self.config["max_trades_per_day"]:
            logging.info("Maximum daily trades reached. Skipping.")
            return False
        
        # Check if we've had too many consecutive losses
        if self.consecutive_losses >= self.config["max_consecutive_losses"]:
            logging.info("Too many consecutive losses. Taking a break.")
            return False
        
        # Check if we've hit our profit target for the day
        if self.current_balance >= self.starting_balance * (1 + self.config["profit_target_percent"]/100):
            logging.info("Daily profit target reached. Stopping for the day.")
            return False
        
        # Check if we've hit our stop loss for the day
        if self.current_balance <= self.starting_balance * (1 - self.config["stop_loss_percent"]/100):
            logging.info("Daily stop loss reached. Stopping for the day.")
            return False
        
        # Check if we're within trading hours
        now = datetime.now()
        if "trading_hours" in self.config:
            if now.hour < self.config["trading_hours"]["start"] or now.hour >= self.config["trading_hours"]["end"]:
                logging.info("Outside of trading hours. Skipping.")
                return False
        
        # Check if we're on a trading day
        if "trading_days" in self.config:
            if now.weekday() not in self.config["trading_days"]:
                logging.info("Not a trading day. Skipping.")
                return False
        
        return True
    
    def execute_trade(self):
        """Execute a trade based on the best available signal"""
        if not self.should_trade():
            return None
        
        asset, direction = self.find_best_asset()
        if not asset or not direction:
            logging.info("No suitable trading opportunities found.")
            return None
        
        # Calculate position size
        amount = self.calculate_position_size()
        
        # Execute the trade
        logging.info(f"📈 Placing {direction.upper()} trade on {asset} for ${amount}")
        
        try:
            trade = self.account.buy(asset, amount, direction, self.config["duration"])
            self.daily_trades += 1
            
            # Monitor trade
            start_time = time.time()
            while time.time() - start_time < self.config["duration"]:
                remaining = int(self.config["duration"] - (time.time() - start_time))
                if remaining % 10 == 0:  # Log every 10 seconds
                    logging.info(f"⏳ Trade in progress... {remaining}s remaining")
                time.sleep(1)
            
            # Check result
            time.sleep(2)  # Wait a bit for settlement
            result = self.account.check_win(trade["id"])
            
            # Update balance
            self.current_balance = self.account.get_balance()
            
            # Update trade history
            trade_record = {
                "asset": asset,
                "direction": direction,
                "amount": amount,
                "result": result,
                "timestamp": datetime.now().isoformat()
            }
            self.trade_history.append(trade_record)
            
            # Update consecutive losses counter
            if result == "loose":  # Note: API returns "loose" instead of "loss"
                self.consecutive_losses += 1
            else:
                self.consecutive_losses = 0
            
            # Log result
            if result == "win":
                all_assets = self.account.get_payment()
                profit = amount * (all_assets[asset]["profit"] / 100)
                logging.info(f"🎯 Trade WON! Profit: ${profit:.2f}")
            elif result == "loose":
                logging.info(f"❌ Trade LOST! Amount: ${amount:.2f}")
            else:
                logging.info(f"Trade result: {result}")
            
            logging.info(f"💰 Current balance: ${self.current_balance}")
            
            return result
        
        except Exception as e:
            logging.error(f"Error executing trade: {e}")
            return None
    
    def run_trading_session(self, duration_hours=1):
        """Run a trading session for the specified duration"""
        if not self.connected:
            if not self.connect():
                return False
            
        logging.info(f"Starting trading session for {duration_hours} hours")
        
        end_time = time.time() + (duration_hours * 60 * 60)
        
        while time.time() < end_time:
            if not self.should_trade():
                break
            
            result = self.execute_trade()
            
            # Wait between trades
            wait_time = 60  # 1 minute
            logging.info(f"Waiting {wait_time} seconds before next analysis...")
            time.sleep(wait_time)
        
        # Session summary
        total_trades = len(self.trade_history)
        if total_trades > 0:
            wins = sum(1 for trade in self.trade_history if trade["result"] == "win")
            win_rate = wins / total_trades
        
            profit = self.current_balance - self.starting_balance
            profit_percent = (profit / self.starting_balance) * 100 if self.starting_balance > 0 else 0
        
            logging.info("=== Trading Session Summary ===")
            logging.info(f"Total trades: {total_trades}")
            logging.info(f"Win rate: {win_rate:.2%}")
            logging.info(f"Starting balance: ${self.starting_balance}")
            logging.info(f"Ending balance: ${self.current_balance}")
            logging.info(f"Profit/Loss: ${profit:.2f} ({profit_percent:.2f}%)")
        else:
            logging.info("No trades were executed during this session.")
        
        return True
    
    def save_trading_history(self, filename="trading_history.json"):
        """Save trading history to a file"""
        with open(filename, 'w') as f:
            json.dump(self.trade_history, f, indent=4)
        logging.info(f"Trading history saved to {filename}")

    def close(self):
        """Close the connection and clean up"""
        if self.connected:
            self.account.close()
            self.connected = False
            logging.info("Connection closed")

# Main execution
if __name__ == "__main__":
    try:
        # Load configuration from file if it exists
        config_file = "config.json"
        if os.path.exists(config_file):
            with open(config_file, 'r') as f:
                user_config = json.load(f)
                CONFIG.update(user_config)
                logging.info(f"Loaded configuration from {config_file}")
        
        bot = TradingBot(CONFIG)
        bot.run_trading_session(duration_hours=2)
        bot.save_trading_history()
    except KeyboardInterrupt:
        logging.info("Trading bot stopped by user")
    except Exception as e:
        logging.error(f"Unexpected error: {e}")
    finally:
        if 'bot' in locals() and hasattr(bot, 'close'):
            bot.close()
        logging.info("👋 Trading bot shutdown complete")
