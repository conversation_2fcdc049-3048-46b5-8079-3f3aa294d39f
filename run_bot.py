#!/usr/bin/env python3
"""
Quick start script for PocketOption Trading Bot
سكريبت تشغيل سريع لبوت تداول PocketOption
"""

import os
import sys
import json
import logging
from datetime import datetime

def check_requirements():
    """Check if all required packages are installed"""
    required_packages = [
        'numpy', 'pandas', 'sklearn', 'scipy', 'requests'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("❌ Missing required packages:")
        for package in missing_packages:
            print(f"   - {package}")
        print("\n📦 Install missing packages with:")
        print("   pip install -r requirements.txt")
        return False
    
    print("✅ All required packages are installed")
    return True

def check_config():
    """Check if config file exists and is valid"""
    if not os.path.exists('config.json'):
        if os.path.exists('config_template.json'):
            print("⚠️  config.json not found. Creating from template...")
            with open('config_template.json', 'r') as template:
                config_data = json.load(template)
            
            with open('config.json', 'w') as config_file:
                json.dump(config_data, config_file, indent=4)
            
            print("✅ config.json created from template")
            print("📝 Please edit config.json with your settings before running the bot")
            return False
        else:
            print("❌ Neither config.json nor config_template.json found")
            return False
    
    try:
        with open('config.json', 'r') as f:
            config = json.load(f)
        
        # Check critical settings
        if config.get('session') == 'YOUR_SESSION_STRING_HERE':
            print("⚠️  Please update your session string in config.json")
            return False
        
        print("✅ Configuration file is valid")
        return True
        
    except json.JSONDecodeError:
        print("❌ config.json is not valid JSON")
        return False

def main():
    """Main function to run the bot"""
    print("🤖 PocketOption Trading Bot - Quick Start")
    print("=" * 50)
    
    # Check requirements
    if not check_requirements():
        sys.exit(1)
    
    # Check configuration
    if not check_config():
        sys.exit(1)
    
    # Import and run the bot
    try:
        print("🚀 Starting trading bot...")
        print("📊 Check trading_logs/ folder for detailed logs")
        print("💾 Backups will be saved in backups/ folder")
        print("⏹️  Press Ctrl+C to stop the bot safely")
        print("-" * 50)
        
        # Import the main bot module
        from po import TradingBot, CONFIG
        
        # Load user configuration
        with open('config.json', 'r') as f:
            user_config = json.load(f)
            CONFIG.update(user_config)
        
        # Create and run bot
        bot = TradingBot(CONFIG)
        
        # Ask user for session duration
        try:
            duration = input("Enter trading session duration in hours (default: 2): ").strip()
            duration = float(duration) if duration else 2.0
        except ValueError:
            duration = 2.0
        
        print(f"⏰ Running trading session for {duration} hours")
        
        # Run the bot
        bot.run_trading_session(duration_hours=duration)
        bot.save_trading_history()
        
    except KeyboardInterrupt:
        print("\n⏹️  Trading bot stopped by user")
    except Exception as e:
        print(f"\n❌ Error running bot: {e}")
        logging.error(f"Bot error: {e}")
    finally:
        if 'bot' in locals() and hasattr(bot, 'close'):
            bot.close()
        print("👋 Trading bot shutdown complete")

if __name__ == "__main__":
    main()
