// qtiltsensor.sip generated by MetaSIP
//
// This file is part of the QtSensors Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_5_1_0 -)

class QTiltReading : public QSensorReading /NoDefaultCtors/
{
%TypeHeaderCode
#include <qtiltsensor.h>
%End

public:
    qreal yRotation() const;
    void setYRotation(qreal y);
    qreal xRotation() const;
    void setXRotation(qreal x);
};

%End
%If (Qt_5_1_0 -)

class QTiltFilter : public QSensorFilter
{
%TypeHeaderCode
#include <qtiltsensor.h>
%End

public:
    virtual bool filter(QTiltReading *reading) = 0;
};

%End
%If (Qt_5_1_0 -)

class QTiltSensor : public QSensor
{
%TypeHeaderCode
#include <qtiltsensor.h>
%End

public:
    explicit QTiltSensor(QObject *parent /TransferThis/ = 0);
    virtual ~QTiltSensor();
    QTiltReading *reading() const;
    void calibrate();

private:
    QTiltSensor(const QTiltSensor &);
};

%End
